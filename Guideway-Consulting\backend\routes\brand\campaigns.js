const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { Campaign } = require("../../database");

const brandCampaignRouter = express.Router();

// GET /api/brand/campaigns - Get all campaigns for the authenticated brand
brandCampaignRouter.get("/", async (req, res) => {
  try {
    // For now, return mock data since we don't have brand-specific campaigns yet
    // In a real implementation, you'd filter by brand/user ID
    const mockCampaigns = [
      {
        id: 1,
        title: 'Glow Serum Ampoule',
        status: 'active',
        description: 'Promote our new vitamin C glow serum to skincare enthusiasts',
        deadline: '2025-06-03',
        budget: 5000,
        targetAudience: 'Skincare enthusiasts, ages 18-35',
        requirements: 'Must have 10K+ followers, skincare niche',
        applicants: 6,
        submissions: 7,
        approved: 9,
        pending: 2,
        rejected: 0,
        submissionRate: 78,
        createdAt: '2025-05-18',
        updatedAt: '2025-05-20'
      },
      {
        id: 2,
        title: 'Vita Bright Toner Pack',
        status: 'draft',
        description: 'Launch campaign for our new vitamin toner pack',
        deadline: null,
        budget: 3000,
        targetAudience: 'Beauty enthusiasts aged 18-30',
        requirements: 'Focus on brightening benefits and daily routine integration',
        applicants: 0,
        submissions: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        submissionRate: 0,
        createdAt: '2025-05-10',
        updatedAt: '2025-05-10'
      },
      {
        id: 3,
        title: 'Collagen Sheet Mask',
        status: 'completed',
        description: 'Promote our premium collagen sheet mask collection',
        deadline: '2025-05-15',
        endedAt: '2025-05-15',
        budget: 4000,
        targetAudience: 'Anti-aging focused consumers aged 25-45',
        requirements: 'Show before/after results and relaxation benefits',
        applicants: 10,
        submissions: 9,
        approved: 10,
        pending: 0,
        rejected: 0,
        submissionRate: 90,
        createdAt: '2025-04-20',
        updatedAt: '2025-05-15'
      },
      {
        id: 4,
        title: 'Vitamin C Essence',
        status: 'active',
        description: 'Showcase our powerful vitamin C essence for glowing skin',
        deadline: '2025-06-10',
        budget: 3500,
        targetAudience: 'Morning skincare routine enthusiasts',
        requirements: 'Demonstrate morning routine integration and glow results',
        applicants: 8,
        submissions: 5,
        approved: 8,
        pending: 1,
        rejected: 0,
        submissionRate: 63,
        createdAt: '2025-05-15',
        updatedAt: '2025-05-18'
      },
      {
        id: 5,
        title: 'Hyaluronic Acid Serum',
        status: 'draft',
        description: 'Promote our new hyaluronic acid serum for deep hydration',
        deadline: null,
        budget: 4500,
        targetAudience: 'Dry skin sufferers, ages 20-50',
        requirements: 'Focus on hydration benefits and skin texture improvement',
        applicants: 0,
        submissions: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        submissionRate: 0,
        createdAt: '2025-05-12',
        updatedAt: '2025-05-12'
      },
      {
        id: 6,
        title: 'Retinol Night Cream',
        status: 'completed',
        description: 'Launch campaign for our anti-aging retinol night cream',
        deadline: '2025-05-10',
        endedAt: '2025-05-10',
        budget: 6000,
        targetAudience: 'Anti-aging focused consumers aged 30+',
        requirements: 'Show nighttime routine and anti-aging benefits',
        applicants: 12,
        submissions: 10,
        approved: 12,
        pending: 0,
        rejected: 0,
        submissionRate: 83,
        createdAt: '2025-04-15',
        updatedAt: '2025-05-10'
      }
    ];

    res.json({
      status: "success",
      campaigns: mockCampaigns
    });
  } catch (error) {
    console.error("Error fetching brand campaigns:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch campaigns"
    });
  }
});

// GET /api/brand/campaigns/:id - Get specific campaign details
brandCampaignRouter.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    
    // Mock data for specific campaign
    const mockCampaigns = {
      1: {
        id: 1,
        title: 'Glow Serum Ampoule',
        status: 'active',
        description: 'Promote our new vitamin C glow serum to skincare enthusiasts',
        deadline: '2025-06-03',
        budget: 5000,
        targetAudience: 'Skincare enthusiasts, ages 18-35',
        requirements: 'Must have 10K+ followers, skincare niche',
        applicants: 6,
        submissions: 7,
        approved: 9,
        pending: 2,
        rejected: 0,
        submissionRate: 78,
        createdAt: '2025-05-18',
        updatedAt: '2025-05-20'
      },
      2: {
        id: 2,
        title: 'Vita Bright Toner Pack',
        status: 'draft',
        description: 'Launch campaign for our new vitamin toner pack',
        deadline: null,
        budget: 3000,
        targetAudience: 'Beauty enthusiasts aged 18-30',
        requirements: 'Focus on brightening benefits and daily routine integration',
        applicants: 0,
        submissions: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        submissionRate: 0,
        createdAt: '2025-05-10',
        updatedAt: '2025-05-10'
      },
      3: {
        id: 3,
        title: 'Collagen Sheet Mask',
        status: 'completed',
        description: 'Promote our premium collagen sheet mask collection',
        deadline: '2025-05-15',
        endedAt: '2025-05-15',
        budget: 4000,
        targetAudience: 'Anti-aging focused consumers aged 25-45',
        requirements: 'Show before/after results and relaxation benefits',
        applicants: 10,
        submissions: 9,
        approved: 10,
        pending: 0,
        rejected: 0,
        submissionRate: 90,
        createdAt: '2025-04-20',
        updatedAt: '2025-05-15'
      },
      4: {
        id: 4,
        title: 'Vitamin C Essence',
        status: 'active',
        description: 'Showcase our powerful vitamin C essence for glowing skin',
        deadline: '2025-06-10',
        budget: 3500,
        targetAudience: 'Morning skincare routine enthusiasts',
        requirements: 'Demonstrate morning routine integration and glow results',
        applicants: 8,
        submissions: 5,
        approved: 8,
        pending: 1,
        rejected: 0,
        submissionRate: 63,
        createdAt: '2025-05-15',
        updatedAt: '2025-05-18'
      },
      5: {
        id: 5,
        title: 'Hyaluronic Acid Serum',
        status: 'draft',
        description: 'Promote our new hyaluronic acid serum for deep hydration',
        deadline: null,
        budget: 4500,
        targetAudience: 'Dry skin sufferers, ages 20-50',
        requirements: 'Focus on hydration benefits and skin texture improvement',
        applicants: 0,
        submissions: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        submissionRate: 0,
        createdAt: '2025-05-12',
        updatedAt: '2025-05-12'
      },
      6: {
        id: 6,
        title: 'Retinol Night Cream',
        status: 'completed',
        description: 'Launch campaign for our anti-aging retinol night cream',
        deadline: '2025-05-10',
        endedAt: '2025-05-10',
        budget: 6000,
        targetAudience: 'Anti-aging focused consumers aged 30+',
        requirements: 'Show nighttime routine and anti-aging benefits',
        applicants: 12,
        submissions: 10,
        approved: 12,
        pending: 0,
        rejected: 0,
        submissionRate: 83,
        createdAt: '2025-04-15',
        updatedAt: '2025-05-10'
      }
    };

    const campaign = mockCampaigns[id];
    if (!campaign) {
      return res.json({
        status: "failed",
        message: "Campaign not found"
      });
    }

    res.json({
      status: "success",
      campaign: campaign
    });
  } catch (error) {
    console.error("Error fetching campaign:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch campaign"
    });
  }
});

// DELETE /api/brand/campaigns/:id - Delete a draft campaign
brandCampaignRouter.delete("/:id", async (req, res) => {
  try {
    const { id } = req.params;
    
    // Mock check - only allow deletion of draft campaigns
    const mockCampaigns = {
      1: { status: 'active' },
      2: { status: 'draft' },
      3: { status: 'completed' },
      4: { status: 'active' },
      5: { status: 'draft' },
      6: { status: 'completed' }
    };

    const campaign = mockCampaigns[id];
    if (!campaign) {
      return res.json({
        status: "failed",
        message: "Campaign not found"
      });
    }

    if (campaign.status !== 'draft') {
      return res.json({
        status: "failed",
        message: "Only draft campaigns can be deleted"
      });
    }

    // In a real implementation, you would delete from database here
    // await Campaign.findByIdAndDelete(id);

    res.json({
      status: "success",
      message: "Campaign deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.json({
      status: "failed",
      message: "Failed to delete campaign"
    });
  }
});

module.exports = brandCampaignRouter;
