/** @format */

const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const cloudinary = require('cloudinary').v2;
const { Auth } = require('./routes/Auth');
const { AdminAuth } = require('./routes/Admin/Auth');
const { Campaigns } = require('./routes/Admin/campaigns');
const { CampaignsPublic } = require('./middlewares/public');
const multer = require('multer');
const { user } = require('./routes/user/user');
const { application } = require('./routes/Admin/Applications');
const { registered } = require('./routes/Admin/registered');
const brandRouter = require('./routes/brand');
const creatorRouter = require('./routes/creator');
const upload = multer({ dest: 'uploads/' });
const mongoose = require('mongoose');
const campaignRoutes = require('./routes/user/campaignsubmission');
const CronService = require('./services/cronService');

dotenv.config();

const app = express();
const allowedOrigins = [process.env.FRONTEND_URL];
const PORT = process.env.PORT;

cloudinary.config({
	cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
	api_key: process.env.CLOUDINARY_API_KEY,
	api_secret: process.env.CLOUDINARY_API_SECRET,
});

// CORS configuration - production ready
app.use(cors({
	origin: function (origin, callback) {
		// Allow requests with no origin (like mobile apps or curl requests)
		if (!origin) return callback(null, true);

		// In production, check against allowed origins
		const allowedOrigins = [
			process.env.FRONTEND_URL,
			'https://guideway-consulting.vercel.app',
			'http://localhost:3000',
			'http://localhost:3001',
			'https://matchably.kr',
			'https://www.matchably.kr'
		].filter(Boolean); // Remove any undefined values

		if (allowedOrigins.includes(origin)) {
			return callback(null, true);
		}

		// For development, allow all origins
		if (process.env.NODE_ENV !== 'production') {
			return callback(null, true);
		}

		callback(new Error('Not allowed by CORS'));
	},
	credentials: true,
	methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
	allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(express.json());
app.get('/', (req, res) => {
	res.json({
		app: 'app is live',
		timestamp: new Date().toISOString(),
		routes: {
			auth: '/api/auth',
			admin: '/api/admin',
			campaigns: '/api/user/campaigns',
			upload: '/api/upload'
		}
	});
});

// Health check endpoint
app.get('/health', (req, res) => {
	res.json({
		status: 'healthy',
		timestamp: new Date().toISOString(),
		database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
	});
});

app.use('/api/auth', Auth);
app.use('/api/admin', AdminAuth);
app.use('/api/admin/campaigns', Campaigns);
app.use('/api/admin/applications', application);
app.use('/api/user/campaigns', CampaignsPublic);
app.use('/api/user/campaigns', user);
app.use('/api/admin/users', registered);
app.use('/api/user', campaignRoutes);
app.use('/api/brand', brandRouter);
app.use('/api/creator', creatorRouter);

app.post('/api/upload', upload.single('image'), async (req, res) => {
	if (!req.file) {
		return res.status(400).json({ message: 'No file uploaded' });
	}

	try {
		const result = await cloudinary.uploader.upload(req.file.path);
		res.status(200).json({ imageUrl: result.secure_url });
	} catch (error) {
		console.error(error);
		res.status(500).json({ message: 'Error uploading to Cloudinary' });
	}
});

// Add error handling for uncaught exceptions
process.on('uncaughtException', (err) => {
	console.error('💥 Uncaught Exception:', err);
	CronService.stop();
	process.exit(1);
});

process.on('unhandledRejection', (err) => {
	console.error('💥 Unhandled Rejection:', err);
	CronService.stop();
	process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
	console.log('🛑 SIGTERM received, shutting down gracefully...');
	CronService.stop();
	process.exit(0);
});

process.on('SIGINT', () => {
	console.log('🛑 SIGINT received, shutting down gracefully...');
	CronService.stop();
	process.exit(0);
});

console.log('🔍 Starting server with configuration:');
console.log('PORT:', PORT);
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('MONGO_URL:', process.env.MONGO_URL ? 'Set' : 'Missing');

mongoose
	.connect(process.env.MONGO_URL)
	.then(() => {
		console.log('✅ Connected to MongoDB');
		app.listen(PORT, () => {
			console.log('🚀 Server running on port', PORT);
			console.log('📡 Health check available at: http://localhost:' + PORT + '/health');
			console.log('🔗 API endpoints available at: http://localhost:' + PORT + '/api');

			// Start performance score cron service
			CronService.start();
		});
	})
	.catch((err) => {
		console.error('❌ MongoDB connection failed:', err);
		console.log('🔧 Check your MONGO_URL in .env file');
		process.exit(1);
	});

// app.listen(PORT, () => {
//   console.log("Server running on port", PORT);
// });
